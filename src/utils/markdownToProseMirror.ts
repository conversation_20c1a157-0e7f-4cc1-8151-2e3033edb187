/**
 * Markdown 到 ProseMirror 解析器
 * 基于编辑器 schema 创建解析器，支持 task-list、table 等扩展
 * 确保与 Tiptap 编辑器的完全兼容性
 */

import { MarkdownParser } from "prosemirror-markdown";
import { Schema } from "prosemirror-model";
import { Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import { TaskList } from "@tiptap/extension-task-list";
import { TaskItem } from "@tiptap/extension-task-item";
import MarkdownIt from "markdown-it";

/**
 * 创建与编辑器相同的 schema
 * 这确保了解析器与编辑器使用完全相同的节点和标记定义
 */
function createEditorSchema(): Schema {
  // 创建一个临时编辑器实例来获取 schema
  const tempEditor = new Editor({
    extensions: [
      StarterKit.configure({
        // 使用与 BasicEditor 相同的配置
      }),
      Placeholder.configure({
        placeholder: "temp",
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: "tiptap-table",
        },
      }),
      TableRow,
      TableHeader,
      TableCell,
      TaskList.configure({
        HTMLAttributes: {
          class: "task-list",
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: "task-item",
        },
      }),
    ],
    content: "",
  });

  const schema = tempEditor.schema;
  tempEditor.destroy(); // 清理临时编辑器
  return schema;
}

/**
 * 创建 Markdown 解析器的 token 映射
 * 支持 StarterKit + Table + TaskList 的所有功能
 */
function createTokenMapping(schema: Schema) {
  return {
    // 块级元素
    blockquote: { block: "blockquote" },
    paragraph: { block: "paragraph" },
    list_item: { block: "listItem" },
    bullet_list: { block: "bulletList" },
    ordered_list: { block: "orderedList" },
    heading: {
      block: "heading",
      getAttrs: (tok: any) => ({ level: +tok.attrGet("level") || 1 }),
    },
    code_block: { block: "codeBlock" },
    fence: {
      block: "codeBlock",
      getAttrs: (tok: any) => ({ language: tok.info || null }),
    },
    hr: { block: "horizontalRule" },

    // 任务列表支持
    task_list: { block: "taskList" },
    task_item: {
      block: "taskItem",
      getAttrs: (tok: any) => {
        // 解析任务项的完成状态
        const checked = tok.attrGet("checked");
        return { checked: checked === "true" || checked === true };
      },
    },

    // 表格支持
    table: { block: "table" },
    thead: { ignore: true }, // 表头在 table_row 中处理
    tbody: { ignore: true }, // 表体在 table_row 中处理
    tr: { block: "tableRow" },
    th: { block: "tableHeader" },
    td: { block: "tableCell" },

    // 行内元素
    strong: { mark: "bold" },
    em: { mark: "italic" },
    s: { mark: "strike" },
    code_inline: { mark: "code" },
    link: {
      mark: "link",
      getAttrs: (tok: any) => ({
        href: tok.attrGet("href"),
        title: tok.attrGet("title") || null,
      }),
    },

    // 硬换行
    hardbreak: { node: "hardBreak" },
    softbreak: { node: "hardBreak" },
  };
}

/**
 * 解析结果接口
 */
export interface ParseResult {
  success: boolean;
  contentJson?: any; // ProseMirror JSON
  error?: string;
}

/**
 * Markdown 到 ProseMirror 解析器类
 */
export class MarkdownToProseMirrorParser {
  private schema: Schema;
  private parser: MarkdownParser;

  constructor() {
    this.schema = createEditorSchema();
    const tokens = createTokenMapping(this.schema);

    // 创建 markdown-it 实例，启用任务列表支持
    const md = new MarkdownIt({
      html: true, // 允许 HTML 标签
      breaks: true, // 转换换行符
      linkify: true, // 自动识别链接
    });

    this.parser = new MarkdownParser(this.schema, md, tokens);
  }

  /**
   * 解析 Markdown 文本为 ProseMirror JSON
   * @param markdown Markdown 文本
   * @returns 解析结果
   */
  parse(markdown: string): ParseResult {
    try {
      if (!markdown || typeof markdown !== "string") {
        return {
          success: false,
          error: "Invalid markdown input",
        };
      }

      // 预处理 Markdown：处理任务列表语法
      const processedMarkdown = this.preprocessMarkdown(markdown);

      // 使用 prosemirror-markdown 解析
      const doc = this.parser.parse(processedMarkdown);

      if (!doc) {
        return {
          success: false,
          error: "Failed to parse markdown",
        };
      }

      // 转换为 JSON
      const contentJson = doc.toJSON();

      return {
        success: true,
        contentJson,
      };
    } catch (error) {
      console.error("Markdown parsing error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown parsing error",
      };
    }
  }

  /**
   * 预处理 Markdown 文本
   * 处理一些特殊语法，确保正确解析
   */
  private preprocessMarkdown(markdown: string): string {
    // 处理任务列表语法
    // 将 GitHub 风格的任务列表转换为标准格式
    let processed = markdown
      .replace(/^(\s*)- \[ \]/gm, "$1- [ ] ") // 确保空格格式正确
      .replace(/^(\s*)- \[x\]/gm, "$1- [x] ") // 确保完成任务格式正确
      .replace(/^(\s*)- \[X\]/gm, "$1- [x] "); // 大写 X 转小写

    return processed;
  }

  /**
   * 获取当前使用的 schema
   */
  getSchema(): Schema {
    return this.schema;
  }
}

/**
 * 全局解析器实例（单例模式）
 */
let globalParser: MarkdownToProseMirrorParser | null = null;

/**
 * 获取全局解析器实例
 */
export function getMarkdownParser(): MarkdownToProseMirrorParser {
  if (!globalParser) {
    globalParser = new MarkdownToProseMirrorParser();
  }
  return globalParser;
}

/**
 * 便捷函数：解析 Markdown 为 ProseMirror JSON
 * @param markdown Markdown 文本
 * @returns 解析结果
 */
export function parseMarkdownToProseMirror(markdown: string): ParseResult {
  return getMarkdownParser().parse(markdown);
}

/**
 * 验证 ProseMirror JSON 是否有效
 * @param contentJson ProseMirror JSON
 * @returns 是否有效
 */
export function validateProseMirrorJson(contentJson: any): boolean {
  try {
    if (!contentJson || typeof contentJson !== "object") {
      return false;
    }

    // 基本结构检查
    if (!contentJson.type || !contentJson.content) {
      return false;
    }

    // 检查是否是有效的文档节点
    return contentJson.type === "doc" && Array.isArray(contentJson.content);
  } catch (error) {
    return false;
  }
}
