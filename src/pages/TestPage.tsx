/**
 * 测试页面入口
 * 用于运行各种测试组件
 */

import React, { useState } from "react";
import { Card, Button, Space, Typography, Row, Col, Alert } from "antd";
import {
  ExperimentOutlined,
  ToolOutlined,
  DatabaseOutlined,
} from "@ant-design/icons";

import MigrationCommand from "../components/migration/MigrationCommand";
import ProseMirrorIntegrationTest from "../test/ProseMirrorIntegrationTest";
import MarkdownCleanupTest from "../test/MarkdownCleanupTest";

const { Title, Paragraph } = Typography;

type TestView = "main" | "converter" | "cleanup" | "migration";

const TestPage: React.FC = () => {
  const [currentView, setCurrentView] = useState<TestView>("main");

  const renderMainView = () => (
    <div style={{ padding: "40px", maxWidth: "800px", margin: "0 auto" }}>
      <div style={{ textAlign: "center", marginBottom: "40px" }}>
        <Title level={1}>🧪 Infinity Notes 测试中心</Title>
        <Paragraph type="secondary">
          测试新的内容转换系统和数据迁移工具
        </Paragraph>
      </div>

      <Alert
        message="测试环境说明"
        description="这是一个测试环境，用于验证新的 TipTap 原生转换器和数据迁移功能。请在测试完成后检查所有功能是否正常工作。"
        type="info"
        style={{ marginBottom: 32 }}
      />

      <Row gutter={24}>
        <Col span={8}>
          <Card
            hoverable
            style={{ height: "200px" }}
            cover={
              <div
                style={{
                  height: "120px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <ExperimentOutlined
                  style={{ fontSize: "48px", color: "#1890ff" }}
                />
              </div>
            }
          >
            <Card.Meta
              title="内容转换器测试"
              description="测试 Markdown ↔ ProseMirror JSON 转换，验证任务列表、表格等功能"
            />
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                block
                onClick={() => setCurrentView("converter")}
              >
                开始测试
              </Button>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card
            hoverable
            style={{ height: "200px" }}
            cover={
              <div
                style={{
                  height: "120px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <ExperimentOutlined
                  style={{ fontSize: "48px", color: "#fa8c16" }}
                />
              </div>
            }
          >
            <Card.Meta
              title="Markdown 清理测试"
              description="测试 AI 内容清理功能，验证思维链标记移除效果"
            />
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                block
                onClick={() => setCurrentView("cleanup")}
              >
                开始测试
              </Button>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card
            hoverable
            style={{ height: "200px" }}
            cover={
              <div
                style={{
                  height: "120px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <DatabaseOutlined
                  style={{ fontSize: "48px", color: "#52c41a" }}
                />
              </div>
            }
          >
            <Card.Meta
              title="数据迁移工具"
              description="将现有 Markdown 数据迁移到新的 ProseMirror JSON 格式"
            />
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                block
                onClick={() => setCurrentView("migration")}
              >
                打开工具
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      <div style={{ marginTop: 40 }}>
        <Title level={3}>测试清单</Title>
        <Space direction="vertical" style={{ width: "100%" }}>
          <Card size="small">
            <strong>✅ 基础功能：</strong>
            <ul>
              <li>Markdown 文本解析</li>
              <li>ProseMirror JSON 生成</li>
              <li>编辑器渲染</li>
              <li>往返转换（Markdown → JSON → Markdown）</li>
            </ul>
          </Card>

          <Card size="small">
            <strong>🧪 任务列表功能：</strong>
            <ul>
              <li>基础任务列表 [ ] 和 [x]</li>
              <li>嵌套任务列表</li>
              <li>任务状态切换</li>
              <li>混合内容（任务列表 + 其他元素）</li>
            </ul>
          </Card>

          <Card size="small">
            <strong>📊 复杂内容：</strong>
            <ul>
              <li>表格转换</li>
              <li>多级标题</li>
              <li>有序和无序列表</li>
              <li>文本格式（加粗、斜体等）</li>
            </ul>
          </Card>
        </Space>
      </div>
    </div>
  );

  const renderHeader = () => (
    <div
      style={{
        padding: "16px 24px",
        borderBottom: "1px solid #f0f0f0",
        backgroundColor: "#fff",
      }}
    >
      <Space>
        <Button
          type="text"
          icon={<ToolOutlined />}
          onClick={() => setCurrentView("main")}
        >
          返回主页
        </Button>
        {currentView === "converter" && <span>内容转换器测试</span>}
        {currentView === "migration" && <span>数据迁移工具</span>}
      </Space>
    </div>
  );

  return (
    <div style={{ minHeight: "100vh", backgroundColor: "#f5f5f5" }}>
      {currentView !== "main" && renderHeader()}

      {currentView === "main" && renderMainView()}
      {currentView === "converter" && <ProseMirrorIntegrationTest />}
      {currentView === "cleanup" && <MarkdownCleanupTest />}
      {currentView === "migration" && (
        <MigrationCommand
          notes={[]}
          onMigrationComplete={(migratedNotes) => {
            console.log("Migration completed:", migratedNotes);
          }}
        />
      )}
    </div>
  );
};

export default TestPage;
