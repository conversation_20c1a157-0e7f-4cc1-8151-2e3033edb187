/**
 * Markdown 清理功能测试组件
 * 测试 AI 服务中的 cleanMarkdownContent 方法
 */

import React, { useState } from "react";
import { Button, Card, Space, Typography, Input, Alert } from "antd";
import { PlayCircleOutlined, CheckCircleOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * Markdown 清理测试页面
 */
const MarkdownCleanupTest: React.FC = () => {
  const [testInput, setTestInput] = useState(`<think>
这是思维链内容，应该被移除
</think>

# 测试标题

这是一个**粗体**文本示例。

🤔 **AI正在思考中...**

---

## ✨ 最终答案

这是最终答案内容，应该被保留。

- [ ] 未完成任务
- [x] 已完成任务

## 普通标题

正常的 Markdown 内容应该被保留。`);

  const [cleanedOutput, setCleanedOutput] = useState("");
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // 模拟 cleanMarkdownContent 方法（简化版）
  const cleanMarkdownContent = (content: string): string => {
    if (!content || typeof content !== "string") {
      return "";
    }

    let cleaned = content
      // 移除思维链相关标记
      .replace(/<think>[\s\S]*?<\/think>/gi, "")
      .replace(/<thinking>[\s\S]*?<\/thinking>/gi, "")
      .replace(/🤔 \*\*AI正在思考中\.\.\.\*\*/g, "")
      
      // 移除流式显示标记
      .replace(/^[\s\n]*---[\s\n]*/gm, "")
      .replace(/^##\s*✨\s*最终答案[\s\n]*/gm, "")
      .replace(/^##\s*🎯\s*最终答案[\s\n]*/gm, "")
      
      // 移除多余的空行（保留段落间的单个空行）
      .replace(/\n{3,}/g, "\n\n")
      
      // 清理首尾空白
      .trim();

    // 确保任务列表格式正确
    cleaned = cleaned
      .replace(/^(\s*)- \[ \]/gm, "$1- [ ] ") // 确保未完成任务格式
      .replace(/^(\s*)- \[x\]/gm, "$1- [x] ") // 确保已完成任务格式
      .replace(/^(\s*)- \[X\]/gm, "$1- [x] "); // 大写 X 转小写

    return cleaned;
  };

  // 执行清理测试
  const runCleanupTest = () => {
    try {
      const result = cleanMarkdownContent(testInput);
      setCleanedOutput(result);
      
      // 验证清理结果
      const hasThinkingTags = result.includes("<think>") || result.includes("</think>");
      const hasThinkingMarkers = result.includes("🤔 **AI正在思考中...**");
      const hasSeparators = result.includes("---");
      const hasFinalAnswerHeaders = result.includes("## ✨ 最终答案");
      
      const isClean = !hasThinkingTags && !hasThinkingMarkers && !hasSeparators && !hasFinalAnswerHeaders;
      
      setTestResult({
        success: isClean,
        message: isClean 
          ? "✅ 清理成功！所有思维链标记和UI标识符已移除"
          : `❌ 清理不完整：${[
              hasThinkingTags && "仍有思维链标签",
              hasThinkingMarkers && "仍有思考标记", 
              hasSeparators && "仍有分隔符",
              hasFinalAnswerHeaders && "仍有最终答案标题"
            ].filter(Boolean).join("、")}`
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: `❌ 清理失败：${error instanceof Error ? error.message : "未知错误"}`
      });
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>🧹 Markdown 清理功能测试</Title>
      <Text type="secondary">
        测试 AI 服务中的 cleanMarkdownContent 方法，验证思维链标记和UI标识符的清理效果。
      </Text>

      <Space direction="vertical" size="large" style={{ width: "100%", marginTop: "20px" }}>
        {/* 控制面板 */}
        <Card title="测试控制" size="small">
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={runCleanupTest}
          >
            执行清理测试
          </Button>
        </Card>

        <div style={{ display: "flex", gap: "20px" }}>
          {/* 左侧：输入 */}
          <div style={{ flex: 1 }}>
            <Card title="测试输入（包含思维链标记）" size="small">
              <TextArea
                value={testInput}
                onChange={(e) => setTestInput(e.target.value)}
                rows={15}
                placeholder="输入包含思维链标记的 Markdown 内容..."
                style={{ fontFamily: "monospace" }}
              />
            </Card>
          </div>

          {/* 右侧：输出和结果 */}
          <div style={{ flex: 1 }}>
            <Card title="清理结果" size="small">
              <TextArea
                value={cleanedOutput}
                rows={10}
                readOnly
                placeholder="清理后的内容将显示在这里..."
                style={{ fontFamily: "monospace", backgroundColor: "#f5f5f5" }}
              />
            </Card>

            {/* 测试结果 */}
            {testResult && (
              <Card title="测试结果" size="small" style={{ marginTop: "16px" }}>
                <Alert
                  message={testResult.message}
                  type={testResult.success ? "success" : "error"}
                  icon={<CheckCircleOutlined />}
                  showIcon
                />
              </Card>
            )}

            {/* 清理规则说明 */}
            <Card title="清理规则" size="small" style={{ marginTop: "16px" }}>
              <ul style={{ fontSize: "12px", margin: 0 }}>
                <li>移除 &lt;think&gt; 和 &lt;thinking&gt; 标签</li>
                <li>移除 "🤔 **AI正在思考中...**" 标记</li>
                <li>移除分隔线 "---"</li>
                <li>移除 "## ✨ 最终答案" 标题</li>
                <li>清理多余空行，保留段落间距</li>
                <li>规范化任务列表格式</li>
              </ul>
            </Card>
          </div>
        </div>
      </Space>
    </div>
  );
};

export default MarkdownCleanupTest;
