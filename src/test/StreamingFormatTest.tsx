/**
 * 流式格式测试组件
 * 测试 AI 生成过程中的格式显示问题修复
 */

import React, { useState } from "react";
import { Button, Card, Space, Typography, Alert, Divider } from "antd";
import { PlayCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import BasicEditor from "../components/notes/BasicEditor";
import { marked } from "marked";

const { Title, Text, Paragraph } = Typography;

/**
 * 流式格式测试页面
 */
const StreamingFormatTest: React.FC = () => {
  const [testMarkdown] = useState(`# AI 生成的任务列表

这是一个 AI 生成的便签示例，包含多种格式：

## 任务列表
- [ ] 完成项目文档
- [x] 修复编辑器问题
- [ ] 测试新功能

## 代码示例
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

## 表格
| 功能 | 状态 | 备注 |
|------|------|------|
| 流式显示 | ✅ | 已修复 |
| 编辑模式 | ✅ | 已修复 |

这是一个**粗体**文本和*斜体*文本的示例。`);

  const [isSimulatingStream, setIsSimulatingStream] = useState(false);
  const [streamContent, setStreamContent] = useState("");
  const [finalContent, setFinalContent] = useState("");
  const [editorJson, setEditorJson] = useState<any>(null);

  // 模拟流式生成过程
  const simulateStreaming = async () => {
    setIsSimulatingStream(true);
    setStreamContent("");
    setFinalContent("");
    
    const chunks = testMarkdown.split('\n');
    let accumulated = "";
    
    for (let i = 0; i < chunks.length; i++) {
      accumulated += chunks[i] + '\n';
      setStreamContent(accumulated);
      await new Promise(resolve => setTimeout(resolve, 200)); // 模拟延迟
    }
    
    setIsSimulatingStream(false);
    setFinalContent(accumulated);
  };

  // 转换 Markdown 为 HTML
  const convertToHtml = (markdown: string): string => {
    if (!markdown) return "";
    
    try {
      marked.setOptions({
        breaks: true,
        gfm: true,
      });
      
      const processedMarkdown = markdown
        .replace(/^(\s*)- \[ \]/gm, "$1- [ ] ")
        .replace(/^(\s*)- \[x\]/gm, "$1- [x] ")
        .replace(/^(\s*)- \[X\]/gm, "$1- [x] ");
      
      return marked(processedMarkdown);
    } catch (error) {
      console.warn("Markdown 转换失败:", error);
      return markdown;
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1400px", margin: "0 auto" }}>
      <Title level={2}>🔄 流式格式测试</Title>
      <Paragraph>
        测试 AI 生成过程中的格式显示修复效果，验证流式显示和编辑模式的格式一致性。
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* 控制面板 */}
        <Card title="测试控制" size="small">
          <Space wrap>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={simulateStreaming}
              loading={isSimulatingStream}
            >
              {isSimulatingStream ? "模拟流式生成中..." : "开始模拟流式生成"}
            </Button>
          </Space>
        </Card>

        <div style={{ display: "flex", gap: "20px" }}>
          {/* 左侧：流式显示测试 */}
          <div style={{ flex: 1 }}>
            <Card title="流式显示效果（修复后）" size="small">
              <Alert
                message="修复说明"
                description="流式内容现在会实时转换 Markdown 为 HTML，确保格式正确显示"
                type="info"
                style={{ marginBottom: "16px" }}
              />
              
              <div style={{ 
                border: "1px solid #d9d9d9", 
                borderRadius: "6px", 
                minHeight: "300px",
                backgroundColor: isSimulatingStream ? "#f0f9ff" : "#ffffff"
              }}>
                <BasicEditor
                  content={convertToHtml(streamContent)}
                  contentFormat="html"
                  onChange={() => {}}
                  placeholder="流式内容将在这里显示..."
                  editable={false}
                />
              </div>
              
              {isSimulatingStream && (
                <Alert
                  message="正在流式生成..."
                  type="info"
                  style={{ marginTop: "8px" }}
                  icon={<ExclamationCircleOutlined />}
                />
              )}
            </Card>

            {/* 最终结果 */}
            {finalContent && (
              <Card title="生成完成后（ProseMirror JSON 模式）" size="small" style={{ marginTop: "16px" }}>
                <Alert
                  message="修复说明"
                  description="生成完成后，内容会被解析为 ProseMirror JSON，确保编辑器原生支持"
                  type="success"
                  style={{ marginBottom: "16px" }}
                />
                
                <div style={{ border: "1px solid #d9d9d9", borderRadius: "6px", minHeight: "200px" }}>
                  <BasicEditor
                    content={finalContent}
                    contentFormat="html" // 这里模拟最终会转换为 ProseMirror JSON
                    onChange={() => {}}
                    onJsonChange={setEditorJson}
                    placeholder="最终内容..."
                    editable={true}
                  />
                </div>
              </Card>
            )}
          </div>

          {/* 右侧：对比和状态 */}
          <div style={{ flex: 1 }}>
            <Card title="修复前后对比" size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Alert
                  message="修复前的问题"
                  description={
                    <ul style={{ margin: 0, paddingLeft: "20px" }}>
                      <li>流式显示：显示原始 Markdown 文本</li>
                      <li>点击编辑：又变回 Markdown 文本</li>
                      <li>格式不一致：用户体验差</li>
                    </ul>
                  }
                  type="error"
                  showIcon
                />
                
                <Alert
                  message="修复后的效果"
                  description={
                    <ul style={{ margin: 0, paddingLeft: "20px" }}>
                      <li>流式显示：实时转换为正确格式</li>
                      <li>编辑模式：优先使用 ProseMirror JSON</li>
                      <li>格式一致：完美的用户体验</li>
                    </ul>
                  }
                  type="success"
                  showIcon
                />
              </Space>
            </Card>

            {/* 技术细节 */}
            <Card title="技术实现" size="small" style={{ marginTop: "16px" }}>
              <Space direction="vertical" style={{ width: "100%" }}>
                <div>
                  <Text strong>流式阶段：</Text>
                  <br />
                  <Text type="secondary">Markdown → marked() → HTML → 编辑器显示</Text>
                </div>
                
                <Divider style={{ margin: "8px 0" }} />
                
                <div>
                  <Text strong>完成阶段：</Text>
                  <br />
                  <Text type="secondary">Markdown → ProseMirror JSON → 数据库存储</Text>
                </div>
                
                <Divider style={{ margin: "8px 0" }} />
                
                <div>
                  <Text strong>编辑阶段：</Text>
                  <br />
                  <Text type="secondary">优先使用 ProseMirror JSON → 编辑器原生支持</Text>
                </div>
              </Space>
            </Card>

            {/* JSON 预览 */}
            {editorJson && (
              <Card title="ProseMirror JSON 预览" size="small" style={{ marginTop: "16px" }}>
                <pre style={{ 
                  fontSize: "11px", 
                  maxHeight: "150px", 
                  overflow: "auto",
                  background: "#f5f5f5",
                  padding: "8px",
                  borderRadius: "4px"
                }}>
                  {JSON.stringify(editorJson, null, 2)}
                </pre>
              </Card>
            )}
          </div>
        </div>
      </Space>
    </div>
  );
};

export default StreamingFormatTest;
