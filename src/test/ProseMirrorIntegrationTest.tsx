/**
 * ProseMirror 集成测试组件
 * 测试 Markdown 解析、round-trip 转换等功能
 */

import React, { useState } from "react";
import { Button, Card, Space, Typography, Input, Alert, Divider } from "antd";
import { PlayCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import BasicEditor from "../components/notes/BasicEditor";
import { parseMarkdownToProseMirror, validateProseMirrorJson } from "../utils/markdownToProseMirror";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface TestResult {
  name: string;
  success: boolean;
  message: string;
  details?: any;
}

/**
 * ProseMirror 集成测试页面
 */
const ProseMirrorIntegrationTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testMarkdown, setTestMarkdown] = useState(`# 测试标题

这是一个**粗体**文本和*斜体*文本的示例。

## 列表测试

- 无序列表项 1
- 无序列表项 2
  - 嵌套项 1
  - 嵌套项 2

1. 有序列表项 1
2. 有序列表项 2

## 任务列表测试

- [ ] 未完成任务
- [x] 已完成任务
- [ ] 另一个未完成任务

## 代码测试

这是\`行内代码\`示例。

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

## 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 引用测试

> 这是一个引用块
> 可以包含多行内容

---

这是分隔线上方的内容。`);

  const [editorContent, setEditorContent] = useState("");
  const [editorJson, setEditorJson] = useState<any>(null);

  // 添加测试结果
  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 测试 Markdown 解析
  const testMarkdownParsing = () => {
    try {
      const result = parseMarkdownToProseMirror(testMarkdown);
      
      if (result.success && result.contentJson) {
        addTestResult({
          name: "Markdown 解析测试",
          success: true,
          message: "Markdown 成功解析为 ProseMirror JSON",
          details: {
            nodeCount: result.contentJson.content?.length || 0,
            hasContent: !!result.contentJson.content,
          }
        });
        
        // 设置编辑器内容为解析结果
        setEditorContent(result.contentJson);
      } else {
        addTestResult({
          name: "Markdown 解析测试",
          success: false,
          message: `解析失败: ${result.error}`,
        });
      }
    } catch (error) {
      addTestResult({
        name: "Markdown 解析测试",
        success: false,
        message: `解析异常: ${error instanceof Error ? error.message : "未知错误"}`,
      });
    }
  };

  // 测试 JSON 验证
  const testJsonValidation = () => {
    if (!editorJson) {
      addTestResult({
        name: "JSON 验证测试",
        success: false,
        message: "没有可验证的 JSON 数据",
      });
      return;
    }

    const isValid = validateProseMirrorJson(editorJson);
    addTestResult({
      name: "JSON 验证测试",
      success: isValid,
      message: isValid ? "ProseMirror JSON 格式有效" : "ProseMirror JSON 格式无效",
      details: editorJson,
    });
  };

  // 测试 Round-trip 转换
  const testRoundTrip = () => {
    try {
      // 1. Markdown -> ProseMirror JSON
      const parseResult = parseMarkdownToProseMirror(testMarkdown);
      
      if (!parseResult.success) {
        addTestResult({
          name: "Round-trip 测试",
          success: false,
          message: `第一步解析失败: ${parseResult.error}`,
        });
        return;
      }

      // 2. 验证 JSON 有效性
      const isValidJson = validateProseMirrorJson(parseResult.contentJson);
      
      if (!isValidJson) {
        addTestResult({
          name: "Round-trip 测试",
          success: false,
          message: "生成的 ProseMirror JSON 无效",
        });
        return;
      }

      addTestResult({
        name: "Round-trip 测试",
        success: true,
        message: "Round-trip 转换成功",
        details: {
          originalLength: testMarkdown.length,
          jsonNodeCount: parseResult.contentJson.content?.length || 0,
        }
      });

    } catch (error) {
      addTestResult({
        name: "Round-trip 测试",
        success: false,
        message: `转换异常: ${error instanceof Error ? error.message : "未知错误"}`,
      });
    }
  };

  // 运行所有测试
  const runAllTests = () => {
    clearResults();
    setTimeout(() => testMarkdownParsing(), 100);
    setTimeout(() => testJsonValidation(), 200);
    setTimeout(() => testRoundTrip(), 300);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>🧪 ProseMirror 集成测试</Title>
      <Paragraph>
        测试 Markdown 到 ProseMirror 的解析功能，验证编辑器集成的正确性。
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* 控制面板 */}
        <Card title="测试控制" size="small">
          <Space wrap>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={runAllTests}
            >
              运行所有测试
            </Button>
            <Button onClick={testMarkdownParsing}>测试 Markdown 解析</Button>
            <Button onClick={testJsonValidation}>测试 JSON 验证</Button>
            <Button onClick={testRoundTrip}>测试 Round-trip</Button>
            <Button onClick={clearResults}>清空结果</Button>
          </Space>
        </Card>

        <div style={{ display: "flex", gap: "20px" }}>
          {/* 左侧：输入和编辑器 */}
          <div style={{ flex: 1 }}>
            <Card title="测试 Markdown 输入" size="small">
              <TextArea
                value={testMarkdown}
                onChange={(e) => setTestMarkdown(e.target.value)}
                rows={12}
                placeholder="输入要测试的 Markdown 内容..."
              />
            </Card>

            <Card title="ProseMirror 编辑器" size="small" style={{ marginTop: "16px" }}>
              <div style={{ border: "1px solid #d9d9d9", borderRadius: "6px", minHeight: "300px" }}>
                <BasicEditor
                  content={editorContent}
                  contentFormat={typeof editorContent === "object" ? "prosemirror-json" : "html"}
                  onChange={(html) => {
                    console.log("编辑器 HTML 变化:", html.substring(0, 100) + "...");
                  }}
                  onJsonChange={(json) => {
                    setEditorJson(json);
                    console.log("编辑器 JSON 变化:", json);
                  }}
                  placeholder="解析结果将显示在这里..."
                  editable={true}
                />
              </div>
            </Card>
          </div>

          {/* 右侧：测试结果 */}
          <div style={{ flex: 1 }}>
            <Card title="测试结果" size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                {testResults.length === 0 ? (
                  <Text type="secondary">暂无测试结果</Text>
                ) : (
                  testResults.map((result, index) => (
                    <Alert
                      key={index}
                      message={result.name}
                      description={
                        <div>
                          <div>{result.message}</div>
                          {result.details && (
                            <details style={{ marginTop: "8px" }}>
                              <summary>详细信息</summary>
                              <pre style={{ fontSize: "12px", marginTop: "4px" }}>
                                {JSON.stringify(result.details, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      }
                      type={result.success ? "success" : "error"}
                      icon={result.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                      showIcon
                    />
                  ))
                )}
              </Space>
            </Card>

            {/* JSON 预览 */}
            {editorJson && (
              <Card title="当前编辑器 JSON" size="small" style={{ marginTop: "16px" }}>
                <pre style={{ 
                  fontSize: "12px", 
                  maxHeight: "200px", 
                  overflow: "auto",
                  background: "#f5f5f5",
                  padding: "8px",
                  borderRadius: "4px"
                }}>
                  {JSON.stringify(editorJson, null, 2)}
                </pre>
              </Card>
            )}
          </div>
        </div>
      </Space>
    </div>
  );
};

export default ProseMirrorIntegrationTest;
